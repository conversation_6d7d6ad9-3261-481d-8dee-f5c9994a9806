import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    TextInput,
    Modal,
    FlatList,
    Alert,
    ActivityIndicator,
    ScrollView,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import { DataTable } from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';

import { fetchBranchList } from '../../apiHandling/StockAPI/fetchBranchListAPI';
import { fetchBranchDetails } from '../../apiHandling/StockAPI/fetchBranchDetailsAPI';
import { fetchItemList } from '../../apiHandling/StockAPI/fetchItemListAPI';
import { fetchItemDetail } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { fetchBusinessDate } from '../../apiHandling/StockAPI/saveReceivingAPI';
import { saveTransferOut } from '../../apiHandling/StockAPI/saveTransferOutAPI';
import { fetchTransferOutList } from '../../apiHandling/StockAPI/fetchTransferOutListAPI';
import { fetchStock } from '../../apiHandling/StockAPI/fetchStockAPI';

import AsyncStorage from '@react-native-async-storage/async-storage';

const TransferOutScreen = () => {
    const navigation = useNavigation();
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [selectedTransferType, setSelectedTransferType] = useState('');
    const [orderNumber, setOrderNumber] = useState('');
    const [invoiceNumber, setInvoiceNumber] = useState('');
    const [lotNumber, setLotNumber] = useState('');

    // Location & Vehicle Details states
    const [location, setLocation] = useState('');
    const [selectedBranchId, setSelectedBranchId] = useState('');
    const [isTransit, setIsTransit] = useState(false);
    const [selectedVehicleType, setSelectedVehicleType] = useState('');
    const [selectedAddress, setSelectedAddress] = useState('');
    const [distance, setDistance] = useState('');
    const [vehicleNumber, setVehicleNumber] = useState('');
    const [driverName, setDriverName] = useState('');
    const [remarks, setRemarks] = useState('');

    // Location Modal states
    const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);
    const [branchList, setBranchList] = useState([]);
    const [loadingBranches, setLoadingBranches] = useState(false);
    const [locationSearchText, setLocationSearchText] = useState('');

    // Address states
    const [addressList, setAddressList] = useState([]);
    const [loadingAddresses, setLoadingAddresses] = useState(false);

    // Item Details states
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [itemBatch, setItemBatch] = useState('');
    const [selectedPort, setSelectedPort] = useState('');
    const [itemWeight, setItemWeight] = useState('');
    const [itemNos, setItemNos] = useState('');

    // Item management states
    const [itemList, setItemList] = useState([]);
    const [itemDetail, setItemDetail] = useState(null);
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');

    // Stock states
    const [stockList, setStockList] = useState([]);
    const [availableStockQty, setAvailableStockQty] = useState(0);
    const [selectedStockItem, setSelectedStockItem] = useState(null);

    // Table states
    const [tableData, setTableData] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [editingItemIndex, setEditingItemIndex] = useState(null);

    // View modal states
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [transferOutList, setTransferOutList] = useState([]);
    const [filteredTransferOuts, setFilteredTransferOuts] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [showFromDatePicker, setShowFromDatePicker] = useState(false);
    const [showToDatePicker, setShowToDatePicker] = useState(false);
    const [loading, setLoading] = useState(false);
    const [selectedViewBranchId, setSelectedViewBranchId] = useState('');
    const [viewBranchList, setViewBranchList] = useState([]);
    const [loadingViewBranches, setLoadingViewBranches] = useState(false);



    useEffect(() => {
        // Load item list when screen mounts
        loadItemList();
    }, []);

    // Validation helper functions
    const isSaveButtonDisabled = () => {
        return !selectedTransferType || !selectedBranchId || tableData.length === 0;
    };

    const isIndentDetailsDisabled = () => {
        return !selectedTransferType;
    };

    const isLocationDetailsDisabled = () => {
        return !selectedTransferType;
    };

    const isIndentFieldsDisabled = () => {
        return !selectedBranchId || tableData.length > 0;
    };

    const isVehicleFieldsDisabled = () => {
        return selectedVehicleType === 'Own';
    };

    const isItemDetailsDisabled = () => {
        return !selectedTransferType || !selectedBranchId;
    };

    // Load transfer out list when view modal parameters change
    useEffect(() => {
        if (viewModalVisible && selectedViewBranchId && fromDate && toDate) {
            loadTransferOutList();
        }
    }, [viewModalVisible, selectedViewBranchId, fromDate, toDate]);

    // Filter transfer outs when search query changes
    useEffect(() => {
        if (!searchQuery) {
            setFilteredTransferOuts(transferOutList);
        } else {
            const filtered = transferOutList.filter(transferOut =>
                transferOut.DocID.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (transferOut.ISO_Number && transferOut.ISO_Number.toLowerCase().includes(searchQuery.toLowerCase()))
            );
            setFilteredTransferOuts(filtered);
        }
    }, [searchQuery, transferOutList]);

    const loadItemList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        try {
            // Use new stock API to get items with stock quantities
            const stockData = await fetchStock(bearerToken, loginBranchID);
            setStockList(stockData);

            // Also keep the old item list for compatibility
            const data = await fetchItemList(bearerToken, loginBranchID);
            setItemList(data);
        } catch (error) {
            console.error('Error loading item list:', error);
            Alert.alert('Error', 'Failed to load item list');
        }
    };

    // Item selection functions
    const handleItemSelectPress = () => {
        // Reset all item-related state
        setItemName('');
        setItemId('');
        setItemBatch('');
        setItemNos('');
        setItemWeight('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Open item selection modal
        setModalVisible(true);
    };

    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);

        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const detail = await fetchItemDetail(bearerToken, item.ItemID);
        setItemDetail(detail);

        if (!detail) {
            // Default assumption: batch is disabled, sell by weight is false, alt qty is false
            setBatchEnabled(false);
            setSellByWeight(false);
            setAltQtyEnabled(false);
            setItemNos('0');
            setItemWeight('0');
            setItemBatch('');
            return;
        }

        const { BatchEnabled, SellByWeight, AltQtyEnabled } = detail;

        setBatchEnabled(BatchEnabled);
        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);


        // Reset batch field - no longer fetching from API
        setItemBatch('');

        // Set fields based on flags regardless of batch
        if (SellByWeight && AltQtyEnabled) {
            setItemWeight('');
            setItemNos('');
        } else if (SellByWeight) {
            setItemWeight('');
            setItemNos('0');
        } else {
            setItemNos('');
            setItemWeight('0');
        }
    };

    const handleStockItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setSelectedStockItem(item);
        setAvailableStockQty(item.StockQty);

        // Auto-fill batch if available
        if (item.BatchNumber) {
            setItemBatch(item.BatchNumber);
            setBatchEnabled(true);
        } else {
            setItemBatch('');
            setBatchEnabled(false);
        }

        setModalVisible(false);
        setSearchText('');

        // Fetch item details for additional properties
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const detail = await fetchItemDetail(bearerToken, item.ItemID);
            setItemDetail(detail);

            if (!detail) {
                setSellByWeight(false);
                setAltQtyEnabled(false);
                setItemNos('0');
                setItemWeight('0');
                return;
            }

            const { SellByWeight, AltQtyEnabled } = detail;
            setSellByWeight(SellByWeight);
            setAltQtyEnabled(AltQtyEnabled);

            // Set fields based on flags
            if (SellByWeight && AltQtyEnabled) {
                setItemWeight('');
                setItemNos('');
            } else if (SellByWeight) {
                setItemWeight('');
                setItemNos('0');
            } else {
                setItemNos('');
                setItemWeight('0');
            }
        } catch (error) {
            console.error('Error fetching item details:', error);
        }
    };

    // Table management functions
    const handleAddItem = () => {
        if (!itemName || !itemId) {
            Alert.alert('Error', 'Please select an item first');
            return;
        }

        if (batchEnabled && !itemBatch) {
            Alert.alert('Error', 'Please enter a batch number');
            return;
        }

        // Validate stock quantity
        if (selectedStockItem) {
            const requestedQty = parseInt(itemNos) || 0;
            if (requestedQty > availableStockQty) {
                Alert.alert('Error', `Cannot add ${requestedQty} units. Only ${availableStockQty} units available in stock.`);
                return;
            }
            if (requestedQty <= 0) {
                Alert.alert('Error', 'Please enter a valid quantity greater than 0');
                return;
            }
        }

        if (editingItemIndex !== null) {
            // Update existing item (following stock take screen pattern)
            const updatedItems = [...tableData];
            updatedItems[editingItemIndex] = {
                ...updatedItems[editingItemIndex],
                itemId,
                itemName,
                batch: itemBatch || '',
                nos: itemNos || '0',
                kgs: itemWeight || '0',
                remarks: remarks || '',
                sellByWeight: sellByWeight,
                altQtyEnabled: altQtyEnabled,
                batchEnabled: batchEnabled,
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                stockGroupId: itemDetail?.StockGroupId || ''
            };
            setTableData(updatedItems);
            setEditingItemIndex(null);
        } else {
            // Check for duplicate items (same item ID)
            const isDuplicateItem = tableData.some(item => item.itemId === itemId);
            if (isDuplicateItem) {
                Alert.alert('Error', 'This item has already been added');
                return;
            }

            // Check for duplicate batch numbers (if batch is enabled and entered)
            if (batchEnabled && itemBatch) {
                const isDuplicateBatch = tableData.some(item =>
                    item.batch && item.batch.toLowerCase() === itemBatch.toLowerCase()
                );
                if (isDuplicateBatch) {
                    Alert.alert('Error', 'This batch number has already been used');
                    return;
                }
            }

            const newItem = {
                lineNo: String(tableData.length + 1).padStart(3, '0'),
                itemId,
                itemName,
                batch: itemBatch || '',
                nos: itemNos || '0',
                kgs: itemWeight || '0',
                remarks: remarks || '',
                id: Date.now(), // Unique row ID
                selected: false,
                // Store item details for save API
                sellByWeight: sellByWeight,
                altQtyEnabled: altQtyEnabled,
                batchEnabled: batchEnabled,
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                stockGroupId: itemDetail?.StockGroupId || ''
            };

            setTableData(prev => [...prev, newItem]);
        }
        handleClearFields(); // Reset form after add/update
    };

    const handleClearFields = () => {
        setItemName('');
        setItemId('');
        setItemBatch('');
        setItemNos('');
        setItemWeight('');
        setRemarks('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSelectedPort(null);
        setItemDetail(null);
        setEditingItemIndex(null); // Reset editing state

        // Clear selected stock item to hide stock display
        setSelectedStockItem(null);
        setAvailableStockQty(0);
    };

    // Function to handle item row click for editing (following stock take screen pattern)
    const handleItemRowClick = (item, index) => {
        setEditingItemIndex(index);
        setItemName(item.itemName);
        setItemId(item.itemId);
        setItemBatch(item.batch);
        setItemNos(item.nos);
        setItemWeight(item.kgs);
        setRemarks(item.remarks || '');
        setBatchEnabled(item.batchEnabled);
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);

        // Set item detail if available
        if (item.itemFamilyID || item.stockGroupId) {
            setItemDetail({
                ItemFamilyID: item.itemFamilyID,
                StockGroupId: item.stockGroupId,
                SellByWeight: item.sellByWeight,
                AltQtyEnabled: item.altQtyEnabled,
                BatchEnabled: item.batchEnabled
            });
        }
    };

    const handleResetAndReload = () => {
        // Reset all form states
        setSelectedScrollOption('');
        setSelectedTransferType('');
        setOrderNumber('');
        setInvoiceNumber('');
        setLotNumber('');

        // Reset location & vehicle details
        setLocation('');
        setSelectedBranchId('');
        setIsTransit(false);
        setSelectedVehicleType('');
        setSelectedAddress('');
        setDistance('');
        setVehicleNumber('');
        setDriverName('');

        // Reset item details
        handleClearFields();

        // Reset table data
        setTableData([]);
        setSelectedRows([]);

        // Reset modal states
        setModalVisible(false);
        setIsLocationModalVisible(false);

        // Reset lists
        setBranchList([]);
        setAddressList([]);

        // Reload item list
        loadItemList();
    };

    const deleteSelectedItems = () => {
        const filtered = tableData.filter(item => !selectedRows.includes(item.id));
        const reIndexed = filtered.map((item, index) => ({
            ...item,
            lineNo: String(index + 1).padStart(3, '0'),
        }));
        setTableData(reIndexed);
        setSelectedRows([]);
    };
    const getISTISOString = () => {
        const now = new Date();

        // Offset IST (+5:30) in milliseconds
        const istOffset = 5.5 * 60 * 60 * 1000;
        const istTime = new Date(now.getTime() + istOffset);

        // Format as ISO string (will still end in Z, but value is IST)
        return istTime.toISOString(); // Includes milliseconds and Z
    };

    const handleSave = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
        const loginUserID = user.userId;
        try {
            // Validation
            if (!selectedTransferType) {
                Alert.alert('Error', 'Please select transfer type');
                return;
            }
            if (!selectedBranchId) {
                Alert.alert('Error', 'Please select location');
                return;
            }
            if (tableData.length === 0) {
                Alert.alert('Error', 'Please add at least one item');
                return;
            }

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const systemDate = getISTISOString();

            const payload = {
                trOutId: "",
                branchId: loginBranchID,
                transTypeId: "TROUT",
                tranSubTypeId: selectedTransferType,
                taxStructureCode: "STATEGST",
                businessDate: businessDate,
                trOrderId: orderNumber || "",
                remarks: "",
                toBranchId: selectedBranchId,
                isO_Number: invoiceNumber || "",
                delieveryMode: "",
                vehicleOwnerType: selectedVehicleType?.toLowerCase() || "own",
                vehicleOwnerBranchId: loginBranchID,
                deliveryStatusCode: "",
                driverName: driverName || "",
                deliveryAddressID: selectedAddress || "00001",
                kmDistance: parseInt(distance) || 0,
                refIndentId: "",
                vehicleNumber: vehicleNumber || "",
                deleted: "N",
                posted: true,
                isKISettled: true,
                createdUserId: loginUserID,
                createdDate: systemDate,
                modifiedUserId: "string",
                modifiedDate: "1753-01-01T00:00:00.000Z",
                deletedUserId: "string",
                deletedDate: "1753-01-01T00:00:00.000Z",
                postedUserID: "string",
                postedDate: "1753-01-01T00:00:00.000Z",
                trOutDetails: tableData.map(item => ({
                    lineNumber: item.lineNo,
                    itemName: item.itemName,
                    batchNumber: item.batch || "",
                    nos: item.nos || "",
                    kgs: item.kgs || "",
                    altQty: 0,
                    qty: parseFloat(item.kgs) || parseFloat(item.nos) || 0,
                    remarks: item.remarks || "",
                    stockQty: 0,
                    stockAltQty: 0,
                    wScale: 0,
                    sellByWeight: item.sellByWeight || false,
                    unitGrossWt: 0,
                    altQtyEnabled: item.altQtyEnabled || false,
                    itemID: item.itemId,
                    groupStockQty: 0,
                    itemStatusId: "OK",
                    binID: "OKBIN",
                    rate: 0,
                    taxCategoryId: "0000000002",
                    taxPercent: 0,
                    unitMRP: 0,
                    productAmount: 0,
                    totalAmount: 0,
                    itemFamilyId: item.itemFamilyID || "",
                    stockGroupId: item.stockGroupId || "",
                    batchEnabled: item.batchEnabled || false,
                    deleted: "N",
                    batchUseByDate: "1753-01-01T00:00:00.000Z",
                    createdUserId: loginUserID,
                    createdDate: systemDate,
                    modifiedUserId: "string",
                    modifiedDate: "1753-01-01T00:00:00.000Z",
                    deletedUserId: "string",
                    deletedDate: "1753-01-01T00:00:00.000Z",
                    dml: "i"
                }))
            };

            const result = await saveTransferOut(bearerToken, payload);

            if (result.result === 1) {
                // Show success dialog with print option (following stock take screen pattern)
                Alert.alert(
                    'Success',
                    `Transfer out saved successfully\nTransfer Out ID: ${result.description}\n\nDo you want to print the transfer out?`,
                    [
                        {
                            text: 'No',
                            onPress: () => {
                                handleResetAndReload();
                            },
                            style: 'cancel'
                        },
                        {
                            text: 'Yes',
                            onPress: () => {
                                // Reset page first, then navigate
                                handleResetAndReload();
                                fetchLatestTransferOutAndNavigate(result.description);
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Save Failed', result.description || 'Unexpected response from server');
            }
        } catch (error) {
            console.error('Save error:', error);
            Alert.alert('Error', 'Failed to save receiving data');
        }
    };

    // Function to fetch latest transfer out and navigate to view screen (following stock take screen pattern)
    const fetchLatestTransferOutAndNavigate = async (trOutId) => {
        try {
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Navigate to ViewTransferOutScreen with the saved transfer out details
            navigation.navigate('ViewTransferOutScreen', {
                trOutId: trOutId,
                docId: trOutId // Using trOutId as docId for now
            });
        } catch (error) {
            console.error('Error navigating to view transfer out:', error);
            Alert.alert('Error', 'Failed to navigate to view transfer out');
            handleResetAndReload();
        }
    };



    // Function to load branch list
    const loadBranchList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            setLoadingBranches(true);
            const branches = await fetchBranchList(bearerToken);

            // Get current login branch ID to exclude from location selection
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Filter out current login branch from the list
            const filteredBranches = branches.filter(branch => branch.branchID !== loginBranchID);
            setBranchList(filteredBranches);
        } catch (error) {
            console.error('Error loading branch list:', error);
            Alert.alert('Error', 'Failed to load branch list');
        } finally {
            setLoadingBranches(false);
        }
    };

    // Function to handle location select button press
    const handleLocationSelectPress = () => {
        setLocationSearchText(''); // Reset search text when opening modal
        setIsLocationModalVisible(true);
        if (branchList.length === 0) {
            loadBranchList();
        }
    };

    // Function to load branch details (addresses)
    const loadBranchDetails = async (branchId) => {
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            setLoadingAddresses(true);
            const addresses = await fetchBranchDetails(bearerToken, branchId);
            setAddressList(addresses);
        } catch (error) {
            console.error('Error loading branch details:', error);
            Alert.alert('Error', 'Failed to load branch addresses');
        } finally {
            setLoadingAddresses(false);
        }
    };



    // Function to handle branch selection
    const handleBranchSelection = async (branch) => {
        setSelectedBranchId(branch.branchID);
        setLocation(`${branch.branchID} - ${branch.branchName}`);
        setIsLocationModalVisible(false);

        // Load related data when branch is selected
        await loadBranchDetails(branch.branchID);
    };

    // Function to close location modal
    const closeLocationModal = () => {
        setIsLocationModalVisible(false);
        setLocationSearchText(''); // Reset search text when closing modal
    };

    // View modal functions
    const openViewModal = async () => {
        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            // Load branch list for view modal
            const branches = await fetchBranchList(bearerToken);

            // Get current login branch ID to exclude from dropdown
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Filter out current login branch from the list
            const filteredBranches = branches.filter(branch => branch.branchID !== loginBranchID);
            setViewBranchList(filteredBranches);

            // Set default dates
            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const businessDateObj = new Date(businessDate);
            const nextDay = new Date(businessDateObj);
            nextDay.setDate(nextDay.getDate() + 1);

            setFromDate(businessDateObj);
            setToDate(nextDay);

            // Reset selected branch to null (no default selection)
            setSelectedViewBranchId(null);
            setSearchQuery('');
            setViewModalVisible(true);
        } catch (err) {
            console.error('Error opening view modal:', err);
            Alert.alert('Error', 'Failed to load view modal');
        }
        setLoading(false);
    };

    const loadTransferOutList = async () => {
        if (!selectedViewBranchId || !fromDate || !toDate) {
            return;
        }

        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            const fromDateStr = fromDate.toISOString().split('T')[0].replace(/-/g, '');
            const toDateStr = toDate.toISOString().split('T')[0].replace(/-/g, '');

            const transferOuts = await fetchTransferOutList(bearerToken, selectedViewBranchId, fromDateStr, toDateStr);

            // Ensure we have an array
            const transferOutsArray = Array.isArray(transferOuts) ? transferOuts : [];

            // Sort by DocID
            const sortedTransferOuts = transferOutsArray.sort((a, b) => {
                const docIdA = parseInt(a.DocID) || 0;
                const docIdB = parseInt(b.DocID) || 0;
                return docIdA - docIdB;
            });

            setTransferOutList(sortedTransferOuts);
            setFilteredTransferOuts(sortedTransferOuts);
        } catch (err) {
            console.error('Error loading transfer out list:', err);
            Alert.alert('Error', 'Failed to load transfer out list');
        }
        setLoading(false);
    };

    const handleTransferOutSelect = (transferOut) => {
        setViewModalVisible(false);
        navigation.navigate('ViewTransferOutScreen', {
            trOutId: transferOut.TrOutID,
            docId: transferOut.DocID
        });
    };

    // Helper function to format date as DD/MM/YYYY
    const formatDateDDMMYYYY = (date) => {
        if (!date) return '';
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };


    return (
        <View style={styles.container}>
            {/* <Navbar/> */}
            <Navbar />
            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>

                <View style={styles.scrollOptions_row}>
                    {/* Back Arrow with Receiving Text */}
                    <TouchableOpacity style={styles.scrollOptions_backContainer}>
                        {/*<Text style={styles.scrollOptions_backArrow}>←</Text>*/}
                        <Text style={styles.scrollOptions_screenTitle}>Transfer Out</Text>
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.scrollOptions_buttonsContainer}>
                        {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
                            const isSelected = selectedScrollOption === option;
                            let buttonStyle = [styles.scrollOptions_button];

                            if (option === 'Cancel') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                                });
                            } else if (option === 'Save') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02720F' : '#02A515',
                                });
                            } else {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                                });
                            }

                            return (
                                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                    <TouchableOpacity
                                        style={[
                                            buttonStyle,
                                            option === 'Save' && isSaveButtonDisabled() && { opacity: 0.5 }
                                        ]}
                                        disabled={option === 'Save' && isSaveButtonDisabled()}
                                        onPress={() => {
                                            setSelectedScrollOption(option);
                                            if (option === 'Save') {
                                                handleSave();
                                            } else if (option === 'View') {
                                                openViewModal();
                                            } else if (option === 'New' || option === 'Cancel') {
                                                handleResetAndReload();
                                            }
                                        }}
                                    >
                                        <Text style={[
                                            styles.scrollOptions_buttonText,
                                            { color: isSelected ? 'white' : 'black' },
                                        ]}>
                                            {option}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
            {/* Indent Details Container */}
            <View style={[
                styles.indentDetails_container,
                isIndentDetailsDisabled() && { opacity: 0.5 }
            ]}>
                {/* Row 1: Indent Details Title */}
                <View style={styles.indentDetails_row}>
                    <Text style={styles.indentDetails_title}>Indent details</Text>
                </View>

                {/* Row 2: Transfer Type Dropdown, Choose Order, Select Button */}
                <View style={styles.indentDetails_inputRow}>
                    <View style={styles.indentDetails_transferTypeContainer}>
                        <Dropdown
                            data={[
                                { label: 'IB Branch Transfer', value: 'IB ST' },

                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Transfer Type"
                            value={selectedTransferType}
                            onChange={(item) => setSelectedTransferType(item.value)}
                            style={styles.indentDetails_dropdown}
                        />
                    </View>

                    <TextInput
                        style={[
                            styles.indentDetails_textField,
                            isIndentFieldsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Choose order"
                        value={orderNumber}
                        onChangeText={setOrderNumber}
                        editable={!isIndentFieldsDisabled()}
                    />

                    <TouchableOpacity
                        style={[
                            styles.indentDetails_selectButton,
                            isIndentFieldsDisabled() && { opacity: 0.5 }
                        ]}
                        disabled={isIndentFieldsDisabled()}
                    >
                        <Text style={styles.indentDetails_selectButtonText}>Select</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Invoice, Lot */}
                <View style={styles.indentDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.indentDetails_textField,
                            isIndentFieldsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Invoice"
                        value={invoiceNumber}
                        onChangeText={setInvoiceNumber}
                        editable={!isIndentFieldsDisabled()}
                    />

                    <TextInput
                        style={[
                            styles.indentDetails_textField,
                            isIndentFieldsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Lot"
                        value={lotNumber}
                        onChangeText={setLotNumber}
                        editable={!isIndentFieldsDisabled()}
                    />
                </View>
            </View>

            {/* Location & Vehicle Details Container */}
            <View style={[
                styles.locationVehicle_container,
                isLocationDetailsDisabled() && { opacity: 0.5 }
            ]}>
                {/* Row 1: Location & Vehicle Details Title */}
                <View style={styles.locationVehicle_row}>
                    <Text style={styles.locationVehicle_title}>Location & Vehicle details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Transit Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            isLocationDetailsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Choose location"
                        value={location}
                        onChangeText={setLocation}
                        editable={!isLocationDetailsDisabled()}
                    />

                    <TouchableOpacity
                        style={[
                            styles.locationVehicle_selectButton,
                            isLocationDetailsDisabled() && { opacity: 0.5 }
                        ]}
                        onPress={handleLocationSelectPress}
                        disabled={isLocationDetailsDisabled()}
                    >
                        <Text style={styles.locationVehicle_buttonText}>Select</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.locationVehicle_transitButton,
                            { backgroundColor: isTransit ? '#02096A' : '#DEDDDD' },
                            isLocationDetailsDisabled() && { opacity: 0.5 }
                        ]}
                        onPress={() => setIsTransit(!isTransit)}
                        disabled={isLocationDetailsDisabled()}
                    >
                        <Text style={[
                            styles.locationVehicle_buttonText,
                            { color: isTransit ? 'white' : 'black' }
                        ]}>
                            Transit
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Vehicle Type Dropdown, Address Dropdown, New Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Own', value: 'Own' },
                                { label: 'Transporter', value: 'Transporter' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select vehicle type"
                            value={selectedVehicleType}
                            onChange={(item) => setSelectedVehicleType(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={addressList.map(address => ({
                                label: `${address.AddressID ?? ''}  ${address.AreaName ?? ''}`,
                                value: address.AddressID ?? ''
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Address"
                            value={selectedAddress}
                            onChange={(item) => setSelectedAddress(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <TouchableOpacity style={styles.locationVehicle_newButton}>
                        <Text style={styles.locationVehicle_buttonText}>New</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 4: Distance, Vehicle Number, Driver */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            isLocationDetailsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Distance"
                        value={distance}
                        onChangeText={setDistance}
                        keyboardType="numeric"
                        editable={!isLocationDetailsDisabled()}
                    />

                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            (isLocationDetailsDisabled() || isVehicleFieldsDisabled()) && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Vehicle number"
                        value={vehicleNumber}
                        onChangeText={setVehicleNumber}
                        editable={!isLocationDetailsDisabled() && !isVehicleFieldsDisabled()}
                    />

                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            (isLocationDetailsDisabled() || isVehicleFieldsDisabled()) && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Driver name"
                        value={driverName}
                        onChangeText={setDriverName}
                        editable={!isLocationDetailsDisabled() && !isVehicleFieldsDisabled()}
                    />
                </View>


            </View>


            {/* Item Details Container */}
            <View style={[
                styles.itemDetails_container,
                isItemDetailsDisabled() && { opacity: 0.5 }
            ]}>
                {/* Row 1: Item Details Title */}
                <View style={styles.itemDetails_row}>
                    <Text style={styles.itemDetails_title}>Item details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Choose Batch, Select Button, Select Port */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            isItemDetailsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Choose Item"
                        value={itemName}
                        onChangeText={setItemName}
                        editable={!isItemDetailsDisabled()}
                    />

                    <TouchableOpacity
                        style={[
                            styles.itemDetails_selectButton,
                            isItemDetailsDisabled() && { opacity: 0.5 }
                        ]}
                        onPress={handleItemSelectPress}
                        disabled={isItemDetailsDisabled()}
                    >
                        <Text style={styles.itemDetails_buttonText}>Select</Text>
                    </TouchableOpacity>

                    {/* Available Stock Display - only show when item is selected but not yet added */}
                    {selectedStockItem && itemName && !tableData.some(item => item.itemId === itemId) && (
                        <View style={styles.stock_display_container}>
                            <Text style={styles.stock_display_label}>Available Stock:</Text>
                            <Text style={styles.stock_display_value}>{availableStockQty} units</Text>
                        </View>
                    )}

                    {/* Item Selection Modal (following stock take pattern with big squared boxes) */}
                    <Modal visible={modalVisible} transparent animationType="slide">
                        <View style={styles.dialog_overlay}>
                            <View style={styles.dialog_container}>
                                {/* Header */}
                                <Text style={styles.dialog_title}>Select Item</Text>



                                {/* Search Input */}
                                <View style={styles.dialog_section}>
                                    <Text style={styles.dialog_label}>Search:</Text>
                                    <TextInput
                                        style={styles.dialog_input}
                                        placeholder="Search items..."
                                        value={searchText}
                                        onChangeText={setSearchText}
                                    />
                                </View>

                                {/* Items List (following stock take grid pattern) */}
                                <ScrollView style={styles.dialog_content}>
                                    <View style={styles.dialog_grid}>
                                        {stockList
                                            .filter(
                                                (item) =>
                                                    item.ItemName.toLowerCase().includes(searchText.toLowerCase()) ||
                                                    item.ItemID.toLowerCase().includes(searchText.toLowerCase()) ||
                                                    item.BatchNumber.toLowerCase().includes(searchText.toLowerCase())
                                            )
                                            .map((item, index) => {
                                                // Calculate margin for proper 3-button layout
                                                const isThirdInRow = (index + 1) % 3 === 0;
                                                const marginRightValue = isThirdInRow ? 0 : '2%';

                                                // Display text with batch number if available
                                                const displayText = item.BatchNumber
                                                    ? `${item.ItemName}\nBatch: ${item.BatchNumber}`
                                                    : item.ItemName;

                                                return (
                                                    <TouchableOpacity
                                                        key={`${item.ItemID}-${item.BatchNumber || 'no-batch'}-${index}`}
                                                        onPress={() => handleStockItemSelect(item)}
                                                        style={{
                                                            backgroundColor: '#FDC500',
                                                            padding: 15,
                                                            borderRadius: 12,
                                                            width: '32%',
                                                            aspectRatio: 1,
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            marginBottom: 15,
                                                            marginRight: marginRightValue,
                                                            shadowColor: '#000',
                                                            shadowOffset: {
                                                                width: 0,
                                                                height: 2,
                                                            },
                                                            shadowOpacity: 0.25,
                                                            shadowRadius: 3.84,
                                                            elevation: 5,
                                                        }}
                                                    >
                                                        <Text style={{
                                                            color: '#000',
                                                            textAlign: 'center',
                                                            fontWeight: 'bold',
                                                            fontSize: 12,
                                                        }}>
                                                            {displayText}
                                                        </Text>

                                                    </TouchableOpacity>
                                                );
                                            })}
                                    </View>
                                </ScrollView>

                                {/* Close Button */}
                                <TouchableOpacity
                                    style={styles.dialog_close_button}
                                    onPress={() => setModalVisible(false)}
                                >
                                    <Text style={styles.dialog_close_text}>Close</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </Modal>

                    {batchEnabled && (
                        <TextInput
                            style={styles.itemDetails_textField}
                            placeholder="Enter batch number"
                            value={itemBatch}
                            onChangeText={setItemBatch}
                        />
                    )}

                    <View style={styles.itemDetails_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Port 1', value: 'Port 1' },
                                { label: 'Port 2', value: 'Port 2' },
                                { label: 'Port 3', value: 'Port 3' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select port"
                            value={selectedPort}
                            onChange={(item) => setSelectedPort(item.value)}
                            style={styles.itemDetails_dropdown}
                        />
                    </View>
                </View>

                {/* Row 3: Wt(kg), Nos */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            { backgroundColor: (sellByWeight) ? 'white' : '#f0f0f0' }
                        ]}
                        placeholder="Wt(kg)"
                        value={itemWeight}
                        onChangeText={setItemWeight}
                        keyboardType="numeric"
                        editable={sellByWeight}
                    />

                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            { backgroundColor: (!sellByWeight || altQtyEnabled) ? 'white' : '#f0f0f0' }
                        ]}
                        placeholder="Nos"
                        value={itemNos}
                        onChangeText={setItemNos}
                        keyboardType="numeric"
                        editable={!sellByWeight || altQtyEnabled}
                    />

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Remarks"
                        value={remarks}
                        onChangeText={setRemarks}
                    />
                </View>

                {/* Row 4: Add Button, Remove Button */}
                <View style={styles.itemDetails_buttonRow}>
                    <TouchableOpacity
                        style={styles.itemDetails_addButton}
                        onPress={handleAddItem}
                    >
                        <Text style={styles.itemDetails_buttonText}>Add</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.itemDetails_removeButton}
                        onPress={handleClearFields}
                    >
                        <Text style={styles.itemDetails_buttonText}>Clear</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 5: Table */}
                <View style={styles.itemDetails_tableContainer}>
                    <DataTable>
                        <DataTable.Header style={styles.itemDetails_tableHeader}>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Select</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Line Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item ID</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item Name</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Batch Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Nos</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Kgs</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Remarks</Text></DataTable.Title>
                        </DataTable.Header>

                        {tableData.map((item, index) => (
                            <TouchableOpacity
                                key={item.id}
                                onPress={() => handleItemRowClick(item, index)}
                                style={{ opacity: editingItemIndex === index ? 0.7 : 1 }}
                            >
                                <DataTable.Row>
                                    <DataTable.Cell>
                                        <TouchableOpacity
                                            onPress={(e) => {
                                                e.stopPropagation(); // Prevent row click when selecting checkbox
                                                if (selectedRows.includes(item.id)) {
                                                    setSelectedRows(selectedRows.filter(id => id !== item.id));
                                                } else {
                                                    setSelectedRows([...selectedRows, item.id]);
                                                }
                                            }}
                                        >
                                            <CheckBox
                                                value={selectedRows.includes(item.id)}
                                                onValueChange={() => {}} // Handled by TouchableOpacity
                                            />
                                        </TouchableOpacity>
                                    </DataTable.Cell>
                                    <DataTable.Cell>{item.lineNo}</DataTable.Cell>
                                    <DataTable.Cell>{item.itemId}</DataTable.Cell>
                                    <DataTable.Cell>{item.itemName}</DataTable.Cell>
                                    <DataTable.Cell>{item.batch}</DataTable.Cell>
                                    <DataTable.Cell>{item.nos}</DataTable.Cell>
                                    <DataTable.Cell>{item.kgs}</DataTable.Cell>
                                    <DataTable.Cell>{item.remarks}</DataTable.Cell>
                                </DataTable.Row>
                            </TouchableOpacity>
                        ))}
                    </DataTable>
                </View>

                {/* Row 6: Delete Selected Row Button */}
                <View style={styles.itemDetails_deleteButtonContainer}>
                    <TouchableOpacity
                        style={styles.itemDetails_deleteButton}
                        onPress={deleteSelectedItems}
                    >
                        <Text style={styles.itemDetails_buttonText}>Delete Selected Row</Text>
                    </TouchableOpacity>
                </View>
            </View>






            {/* Location Selection Modal */}
            <Modal
                visible={isLocationModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={closeLocationModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Select Location</Text>
                            <TouchableOpacity
                                style={styles.modalCloseButton}
                                onPress={closeLocationModal}
                            >
                                <Text style={styles.modalCloseButtonText}>×</Text>
                            </TouchableOpacity>
                        </View>

                        {/* 1st row: Search bar */}
                        <View style={styles.modalSearchSection}>
                            <TextInput
                                placeholder="Search branches..."
                                value={locationSearchText}
                                onChangeText={setLocationSearchText}
                                style={styles.modalSearchInput}
                            />
                        </View>

                        {loadingBranches ? (
                            <View style={styles.modalLoadingContainer}>
                                <Text style={styles.modalLoadingText}>Loading branches...</Text>
                            </View>
                        ) : (
                            /* From 2nd row: 3 in a row big yellow squared buttons */
                            <FlatList
                                data={branchList.filter(branch =>
                                    branch.branchName.toLowerCase().includes(locationSearchText.toLowerCase()) ||
                                    branch.branchID.toLowerCase().includes(locationSearchText.toLowerCase())
                                )}
                                keyExtractor={(item) => item.branchID}
                                numColumns={3}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        style={styles.modalBranchButton}
                                        onPress={() => handleBranchSelection(item)}
                                    >
                                        <Text style={styles.modalBranchButtonId}>{item.branchID}</Text>
                                        <Text style={styles.modalBranchButtonName}>{item.branchName}</Text>
                                    </TouchableOpacity>
                                )}
                                style={styles.modalBranchList}
                                contentContainerStyle={styles.modalBranchGrid}
                            />
                        )}
                    </View>
                </View>
            </Modal>

            {/* View Transfer Out Modal (following stock take view popup pattern) */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={viewModalVisible}
                onRequestClose={() => setViewModalVisible(false)}
            >
                <View style={styles.dialog_overlay}>
                    <View style={styles.dialog_container}>
                        {/* Header */}
                        <Text style={styles.dialog_title}>View Transfer Out</Text>

                        {/* Branch Selection and Search in same row */}
                        <View style={styles.dialog_first_row}>
                            <View style={styles.dialog_branch_container}>
                                <Text style={styles.dialog_label}>Select Branch:</Text>
                                <Dropdown
                                    data={viewBranchList.map(branch => ({
                                        label: `${branch.branchID} - ${branch.branchName}`,
                                        value: branch.branchID
                                    }))}
                                    labelField="label"
                                    valueField="value"
                                    placeholder="Select Branch"
                                    value={selectedViewBranchId}
                                    onChange={(item) => setSelectedViewBranchId(item.value)}
                                    style={styles.dialog_dropdown}
                                />
                            </View>
                            <View style={styles.dialog_search_container}>
                                <Text style={styles.dialog_label}>Search:</Text>
                                <TextInput
                                    placeholder="Search by Doc ID or ISO Number..."
                                    value={searchQuery}
                                    onChangeText={setSearchQuery}
                                    style={styles.dialog_input}
                                />
                            </View>
                        </View>

                        {/* Date Selection */}
                        <View style={styles.dialog_date_section}>
                            <View style={styles.dialog_date_container}>
                                <Text style={styles.dialog_label}>From Date:</Text>
                                <TouchableOpacity
                                    onPress={() => setShowFromDatePicker(true)}
                                    style={styles.dialog_date_button}
                                >
                                    <Text style={styles.dialog_date_text}>
                                        {fromDate ? formatDateDDMMYYYY(fromDate) : 'Select Date'}
                                    </Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.dialog_date_container}>
                                <Text style={styles.dialog_label}>To Date:</Text>
                                <TouchableOpacity
                                    onPress={() => setShowToDatePicker(true)}
                                    style={styles.dialog_date_button}
                                >
                                    <Text style={styles.dialog_date_text}>
                                        {toDate ? formatDateDDMMYYYY(toDate) : 'Select Date'}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                        {/* Transfer Outs List (following stock take grid pattern) */}
                        <ScrollView style={styles.dialog_content}>
                            <View style={styles.dialog_grid}>
                                {loading && (
                                    <View style={{ width: '100%', alignItems: 'center', padding: 20 }}>
                                        <ActivityIndicator size="large" color="#0000ff" />
                                    </View>
                                )}
                                {!loading && filteredTransferOuts.map((transferOut, index) => {
                                    // Calculate margin for proper 3-button layout (copied from stock take screen)
                                    const isThirdInRow = (index + 1) % 3 === 0;
                                    const marginRightValue = isThirdInRow ? 0 : '2%';

                                    return (
                                        <TouchableOpacity
                                            key={index}
                                            onPress={() => {
                                                handleTransferOutSelect(transferOut);
                                            }}
                                            style={{
                                                backgroundColor: '#FDC500',
                                                padding: 15,
                                                borderRadius: 12,
                                                width: '32%',
                                                aspectRatio: 1,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                marginBottom: 15,
                                                marginRight: marginRightValue,
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.25,
                                                shadowRadius: 3.84,
                                                elevation: 5,
                                            }}
                                        >
                                            <Text style={{
                                                color: '#000',
                                                textAlign: 'center',
                                                fontWeight: 'bold',
                                                fontSize: 14,
                                            }}>
                                                {transferOut.DocID}
                                            </Text>
                                        </TouchableOpacity>
                                    );
                                })}
                            </View>
                        </ScrollView>

                        {/* Close Button */}
                        <TouchableOpacity
                            style={styles.dialog_close_button}
                            onPress={() => setViewModalVisible(false)}
                        >
                            <Text style={styles.dialog_close_text}>Close</Text>
                        </TouchableOpacity>

                        {/* Date Pickers */}
                        {showFromDatePicker && (
                            <DateTimePicker
                                value={fromDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(_, selectedDate) => {
                                    setShowFromDatePicker(false);
                                    if (selectedDate) {
                                        setFromDate(selectedDate);
                                    }
                                }}
                            />
                        )}

                        {showToDatePicker && (
                            <DateTimePicker
                                value={toDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(_, selectedDate) => {
                                    setShowToDatePicker(false);
                                    if (selectedDate) {
                                        setToDate(selectedDate);
                                    }
                                }}
                            />
                        )}
                    </View>
                </View>
            </Modal>

        </View>
    );


};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================
    menu_row: {
        paddingHorizontal: 5,
        alignItems: 'center',
    },
    menu_button: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginHorizontal: 5,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    scrollOptions_backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    scrollOptions_backArrow: {
        fontSize: 20,
        fontWeight: 'bold',
        marginRight: 5,
    },
    scrollOptions_screenTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: '#02096A',
    },
    scrollOptions_buttonsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
    },
    scrollOptions_buttonWrapper: {
        width: '22%',
        marginHorizontal: 5,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,

    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== INDENT DETAILS STYLES ====================
    indentDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 8,
        marginBottom: 4,
        borderRadius: 10,
    },
    indentDetails_row: {
        marginBottom: 8,
    },
    indentDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    indentDetails_transferTypeContainer: {
        flex: 1,
        minWidth: 120,
    },
    indentDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    indentDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    indentDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    indentDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    indentDetails_selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== LOCATION & VEHICLE DETAILS STYLES ====================
    locationVehicle_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 4,
        borderRadius: 10,
    },
    locationVehicle_row: {
        marginBottom: 8,
    },
    locationVehicle_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    locationVehicle_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    locationVehicle_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    locationVehicle_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    locationVehicle_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    locationVehicle_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_transitButton: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_newButton: {
        height: 50,
        backgroundColor: '#097215',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_removeButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== ITEM DETAILS STYLES ====================
    itemDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 8,
        borderRadius: 10,
    },
    itemDetails_row: {
        marginBottom: 8,
    },
    itemDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    itemDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    itemDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 100,
    },
    itemDetails_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    itemDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    itemDetails_buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    itemDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_addButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#02720F',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_removeButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_tableContainer: {
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: 'white',
    },
    itemDetails_tableHeader: {
        backgroundColor: '#041C44',
    },
    itemDetails_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_deleteButtonContainer: {
        alignItems: 'center',
        marginBottom: 8,
    },
    itemDetails_deleteButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        borderWidth: 1,
        borderColor: '#FDC500',
    },

    // ==================== MODAL STYLES ====================
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        width: '80%',
        maxHeight: '70%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        paddingBottom: 10,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#02096A',
    },
    modalCloseButton: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#FF0000',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalCloseButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
    },
    modalLoadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
    modalLoadingText: {
        fontSize: 16,
        color: '#666',
    },
    modalBranchList: {
        maxHeight: 400,
    },
    modalBranchItem: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        backgroundColor: '#F8F8F8',
        marginBottom: 5,
        borderRadius: 8,
    },
    modalBranchId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#02096A',
        marginBottom: 5,
    },
    modalBranchName: {
        fontSize: 14,
        color: '#666',
    },
    modalSearchInput: {
        height: 40,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 10,
        marginBottom: 15,
        backgroundColor: 'white',
    },
    modalSearchSection: {
        marginBottom: 15,
    },
    modalBranchButton: {
        flex: 1,
        margin: 6,
        padding: 15,
        backgroundColor: '#FDC500',
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 80,
        borderWidth: 2,
        borderColor: '#E6B800',
        maxWidth: '30%',
    },
    modalBranchButtonId: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#000',
        marginBottom: 5,
    },
    modalBranchButtonName: {
        fontSize: 12,
        color: '#000',
        textAlign: 'center',
    },
    modalBranchGrid: {
        paddingBottom: 16,
        paddingHorizontal: 10,
    },

    // ==================== VIEW MODAL STYLES (following stock take dialog pattern) ====================
    dialog_overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dialog_container: {
        backgroundColor: '#fff',
        borderRadius: 15,
        padding: 20,
        width: '90%',
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    dialog_title: {
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#002b5c',
    },
    dialog_section: {
        marginBottom: 15,
    },
    dialog_first_row: {
        flexDirection: 'row',
        marginBottom: 15,
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    dialog_branch_container: {
        flex: 1,
        marginRight: 10,
    },
    dialog_search_container: {
        flex: 1,
        marginLeft: 10,
    },
    dialog_label: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#333',
    },
    dialog_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    dialog_input: {
        height: 50,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 10,
        backgroundColor: 'white',
        fontSize: 16,
    },
    dialog_date_section: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 15,
        alignItems: 'flex-start',
    },
    dialog_date_container: {
        flex: 1,
        marginHorizontal: 5,
    },
    dialog_date_button: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        justifyContent: 'center',
    },
    dialog_date_text: {
        fontSize: 16,
        color: '#333',
    },
    dialog_content: {
        maxHeight: 300,
        marginBottom: 15,
    },
    dialog_grid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
    },
    dialog_close_button: {
        backgroundColor: '#002b5c',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },
    dialog_close_text: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },

    // Stock info styles
    stock_info_container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#f0f8ff',
        padding: 12,
        borderRadius: 8,
        marginBottom: 15,
        borderWidth: 1,
        borderColor: '#007bff',
    },
    stock_info_label: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    stock_info_value: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#007bff',
    },

    // Stock display in item details
    stock_display_container: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#e8f5e8',
        padding: 8,
        borderRadius: 6,
        marginLeft: 10,
        borderWidth: 1,
        borderColor: '#4caf50',
    },
    stock_display_label: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        marginRight: 5,
    },
    stock_display_value: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#4caf50',
    },

});

export default TransferOutScreen;
